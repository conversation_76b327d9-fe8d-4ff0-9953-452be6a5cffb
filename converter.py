import json
from datetime import datetime

def format_text(text_entities):
    """Extracts and concatenates text from complex text_entities."""
    if isinstance(text_entities, str):
        return text_entities
    if isinstance(text_entities, list):
        # Join text from all dictionaries/strings in the list
        return ''.join(item.get('text', '') if isinstance(item, dict) else item for item in text_entities)
    return ''

def export_chat_to_txt(json_path, txt_path):
    """
    Reads a JSON chat export and converts it to a simple TXT file.
    A timestamp is only added when the interval between messages is greater than 5 minutes.
    """
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            chat_data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error reading or parsing {json_path}: {e}")
        return

    messages = chat_data.get('messages', [])
    if not messages:
        print("No messages found in the JSON file.")
        return

    last_message_time = None
    time_format = "%Y-%m-%dT%H:%M:%S"

    with open(txt_path, 'w', encoding='utf-8') as f:
        for msg in messages:
            if msg.get('type') != 'message':
                continue

            current_message_time_str = msg.get('date')
            if not current_message_time_str:
                continue
            
            current_message_time = datetime.strptime(current_message_time_str, time_format)

            # Add timestamp if it's the first message or the gap is > 5 minutes
            if last_message_time is None or (current_message_time - last_message_time).total_seconds() > 300:
                f.write(f"\n{current_message_time.strftime('%Y-%m-%d %H:%M:%S')}\n")

            last_message_time = current_message_time

            sender = msg.get('from', 'Unknown')
            text = format_text(msg.get('text', ''))

            # Handle non-text content like photos, files, or stickers
            if not text:
                if 'photo' in msg:
                    text = '[Photo]'
                elif 'file_name' in msg:
                    text = f"[File: {msg.get('file_name')}]"
                elif 'sticker_emoji' in msg:
                    text = f"[Sticker: {msg.get('sticker_emoji')}]"
                elif msg.get('media_type') == 'voice_message':
                    text = '[Voice Message]'
                # Add more placeholders for other media types if necessary
                elif not text:
                    text = '[Empty Message]'
            
            if sender:
                f.write(f"{sender}: {text}\n")
                
    print(f"Successfully exported chat to {txt_path}")

if __name__ == "__main__":
    export_chat_to_txt('result.json', 'chat_history.txt')