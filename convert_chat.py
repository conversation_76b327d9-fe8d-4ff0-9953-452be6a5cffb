#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天记录转换脚本
将JSON格式的聊天记录转换为简单的TXT格式
"""

import json
import sys
from datetime import datetime
from typing import Optional

def parse_datetime(date_str: str) -> datetime:
    """解析ISO格式的日期时间字符串"""
    return datetime.fromisoformat(date_str.replace('Z', '+00:00'))

def format_time(dt: datetime) -> str:
    """格式化时间为 HH:MM:SS"""
    return dt.strftime("%H:%M:%S")

def should_show_time(current_time: datetime, last_time: Optional[datetime]) -> bool:
    """判断是否应该显示时间（间隔大于5分钟）"""
    if last_time is None:
        return True
    
    time_diff = current_time - last_time
    return time_diff.total_seconds() > 300  # 5分钟 = 300秒

def extract_text_content(text_entities: list) -> str:
    """从text_entities中提取纯文本内容"""
    if not text_entities:
        return ""
    
    result = ""
    for entity in text_entities:
        if entity.get("type") == "plain":
            result += entity.get("text", "")
        elif entity.get("type") == "text_link":
            result += entity.get("text", "")
        elif entity.get("type") == "mention":
            result += entity.get("text", "")
        elif entity.get("type") == "hashtag":
            result += entity.get("text", "")
        elif entity.get("type") == "bold":
            result += entity.get("text", "")
        elif entity.get("type") == "italic":
            result += entity.get("text", "")
        elif entity.get("type") == "code":
            result += entity.get("text", "")
        else:
            # 对于其他类型，也尝试获取text字段
            result += entity.get("text", "")
    
    return result

def convert_chat_to_txt(json_file_path: str, output_file_path: str):
    """转换聊天记录从JSON到TXT格式"""
    
    print(f"正在读取文件: {json_file_path}")
    
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        messages = data.get('messages', [])
        print(f"找到 {len(messages)} 条消息")
        
        # 打开输出文件
        with open(output_file_path, 'w', encoding='utf-8') as output_file:
            last_time = None
            
            for i, message in enumerate(messages):
                # 只处理消息类型的记录
                if message.get('type') != 'message':
                    continue
                
                # 获取消息信息
                date_str = message.get('date', '')
                from_user = message.get('from', '未知用户')
                
                # 提取消息文本
                text = message.get('text', '')
                if isinstance(text, list):
                    # 如果text是列表，说明是复杂格式，需要从text_entities提取
                    text_entities = message.get('text_entities', [])
                    text = extract_text_content(text_entities)
                elif not text and 'text_entities' in message:
                    # 如果text为空但有text_entities，从中提取
                    text_entities = message.get('text_entities', [])
                    text = extract_text_content(text_entities)
                
                # 跳过空消息
                if not text.strip():
                    continue
                
                try:
                    # 解析时间
                    current_time = parse_datetime(date_str)
                    
                    # 判断是否需要显示时间
                    if should_show_time(current_time, last_time):
                        time_str = format_time(current_time)
                        output_file.write(f"{time_str}\n")
                    
                    # 写入消息
                    output_file.write(f"{from_user}: {text}\n")
                    
                    last_time = current_time
                    
                except Exception as e:
                    print(f"处理消息 {i+1} 时出错: {e}")
                    continue
                
                # 每处理1000条消息显示进度
                if (i + 1) % 1000 == 0:
                    print(f"已处理 {i+1} 条消息...")
        
        print(f"转换完成！输出文件: {output_file_path}")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式错误 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("用法: python convert_chat.py <输入JSON文件> <输出TXT文件>")
        print("示例: python convert_chat.py result.json chat_output.txt")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    convert_chat_to_txt(input_file, output_file)

if __name__ == "__main__":
    main()
